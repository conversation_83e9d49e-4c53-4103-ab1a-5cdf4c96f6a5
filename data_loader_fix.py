"""
修复后的数据加载器，解决Windows多进程和内存问题
"""
import torch
import torch.multiprocessing as mp
from torch.utils.data import DataLoader
import os
import platform

def get_optimal_dataloader_settings():
    """
    根据操作系统和硬件配置获取最优的DataLoader设置
    """
    system = platform.system()
    
    if system == "Windows":
        # Windows系统的设置
        return {
            'num_workers': 0,  # Windows上禁用多进程
            'pin_memory': False,  # 禁用pin_memory
            'persistent_workers': False,
            'prefetch_factor': 2,
        }
    else:
        # Linux/macOS系统的设置
        return {
            'num_workers': min(4, os.cpu_count()),
            'pin_memory': True,
            'persistent_workers': True,
            'prefetch_factor': 2,
        }

def create_safe_dataloader(dataset, batch_size, shuffle=True, collate_fn=None, **kwargs):
    """
    创建一个安全的DataLoader，自动处理多进程和内存问题
    """
    # 获取最优设置
    optimal_settings = get_optimal_dataloader_settings()
    
    # 合并用户提供的参数
    final_kwargs = {**optimal_settings, **kwargs}
    
    # 确保batch_size不会太大导致内存问题
    max_batch_size = 64 if platform.system() == "Windows" else 256
    actual_batch_size = min(batch_size, max_batch_size)
    
    if actual_batch_size != batch_size:
        print(f"Warning: Batch size reduced from {batch_size} to {actual_batch_size} to avoid memory issues")
    
    # 创建DataLoader
    dataloader = DataLoader(
        dataset,
        batch_size=actual_batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        **final_kwargs
    )
    
    return dataloader

def setup_multiprocessing():
    """
    设置多进程环境
    """
    if platform.system() == "Windows":
        # Windows上设置spawn方法
        try:
            mp.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 如果已经设置过，忽略错误
    
    # 设置共享内存策略
    if hasattr(torch, 'multiprocessing'):
        torch.multiprocessing.set_sharing_strategy('file_system')

# 在模块导入时自动设置
setup_multiprocessing()
