# PMMR-GIN 内存问题解决方案

## 问题分析

你遇到的错误包含两个主要问题：

1. **多进程共享内存错误** (Windows特有)
   - 错误：`RuntimeError: Couldn't open shared file mapping`
   - 原因：Windows上PyTorch多进程数据加载的共享内存机制问题

2. **CUDA内存不足错误**
   - 错误：`torch.OutOfMemoryError: CUDA out of memory`
   - 原因：batch_size太大，模型内存使用不够优化

## 已实施的修复

### 1. 修改的文件

- ✅ `main.py` - 优化训练函数，增加内存管理和错误处理
- ✅ `models/core.py` - 优化模型forward函数，及时释放内存
- ✅ `data_loader_fix.py` - 新增安全的DataLoader创建模块
- ✅ `run_with_memory_fix.py` - 新增启动脚本，自动配置环境
- ✅ `test_memory_fix.py` - 测试脚本验证修复效果

### 2. 核心修复内容

#### 多进程问题修复：
```python
# Windows上自动禁用多进程
num_workers = 0 if os.name == 'nt' else 4
pin_memory = False  # Windows上禁用
prefetch_factor = None  # num_workers=0时必须为None
```

#### 内存优化：
```python
# 减小batch_size
actual_batch_size = min(batch_size, 64)

# 模型中及时释放张量
del compound_x, compound_adj
torch.cuda.empty_cache()

# 训练中定期清理内存
if batch_idx % 10 == 0:
    torch.cuda.empty_cache()
```

#### 环境变量设置：
```bash
PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
CUDA_LAUNCH_BLOCKING=1
OMP_NUM_THREADS=1
MKL_NUM_THREADS=1
```

## 使用方法

### 推荐方法：使用修复后的启动脚本

```bash
# 使用默认参数（推荐）
python run_with_memory_fix.py

# 自定义参数
python run_with_memory_fix.py --batch_size 32 --learning_rate 0.0001 --max_epochs 100
```

### 备选方法：直接运行

```bash
# Windows PowerShell
$env:PYTORCH_CUDA_ALLOC_CONF="expandable_segments:True"
$env:CUDA_LAUNCH_BLOCKING="1"
python main.py --batch_size 32 --num_workers 0

# Windows CMD
set PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
set CUDA_LAUNCH_BLOCKING=1
python main.py --batch_size 32 --num_workers 0
```

## 测试验证

运行快速测试：
```bash
python quick_test.py
```

如果看到"所有测试通过！"，说明修复成功。

## 参数建议

基于你的GPU配置（15.93 GB），建议参数：

```bash
python run_with_memory_fix.py \
    --dataset davis \
    --batch_size 32 \
    --learning_rate 0.0001 \
    --max_epochs 100 \
    --num_workers 0
```

## 如果仍有问题

### 1. 进一步减小内存使用：
```bash
python run_with_memory_fix.py --batch_size 16
```

### 2. 监控内存使用：
在训练过程中观察GPU内存使用情况

### 3. 检查数据文件：
确保蛋白质embedding文件大小合理

## 性能影响说明

- **数据加载速度**：禁用多进程可能稍微降低数据加载速度
- **训练稳定性**：大幅提升，避免崩溃
- **内存使用**：显著降低，避免OOM错误
- **模型性能**：不受影响，只是batch_size较小可能需要更多epoch

## 总结

这些修复应该能够解决你遇到的两个主要问题：

1. ✅ **多进程问题**：通过在Windows上禁用多进程解决
2. ✅ **内存问题**：通过减小batch_size和优化内存管理解决

现在你可以安全地运行训练程序了！
