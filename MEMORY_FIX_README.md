# PMMR-GIN 内存问题修复指南

本文档说明如何解决在Windows环境下运行PMMR-GIN时遇到的内存和多进程问题。

## 问题描述

原始错误包含两个主要问题：

1. **多进程共享内存错误**：
   ```
   RuntimeError: Couldn't open shared file mapping: <torch_14884_1841756581_13>, error code: <1455>
   ```

2. **CUDA内存不足错误**：
   ```
   torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 992.00 MiB.
   ```

## 解决方案

### 1. 修复的文件

- `main.py` - 修改了训练函数和数据加载器，增加了内存管理
- `models/core.py` - 优化了模型的forward函数，减少内存峰值
- `data_loader_fix.py` - 新增的安全数据加载器模块
- `run_with_memory_fix.py` - 新增的启动脚本，自动设置环境变量
- `test_memory_fix.py` - 测试脚本，验证修复是否有效

### 2. 主要修改

#### 多进程问题修复：
- 在Windows上自动禁用多进程（`num_workers=0`）
- 禁用`pin_memory`以避免共享内存问题
- 设置正确的多进程启动方法

#### 内存问题修复：
- 减小默认batch size（从256降到32-64）
- 在模型forward函数中及时释放不需要的张量
- 增加定期的GPU内存清理
- 设置CUDA内存分配策略

#### 环境变量设置：
- `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True`
- `CUDA_LAUNCH_BLOCKING=1`
- `OMP_NUM_THREADS=1`
- `MKL_NUM_THREADS=1`

## 使用方法

### 方法1：使用修复后的启动脚本（推荐）

```bash
# 使用默认参数运行
python run_with_memory_fix.py

# 使用自定义参数运行
python run_with_memory_fix.py --dataset davis --batch_size 32 --learning_rate 0.0001
```

### 方法2：直接运行main.py

```bash
# 设置环境变量后运行
set PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
set CUDA_LAUNCH_BLOCKING=1
python main.py --batch_size 32 --num_workers 0
```

### 方法3：在代码中手动设置

```python
import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# 然后运行你的代码
```

## 测试修复

在运行主程序之前，建议先运行测试脚本：

```bash
python test_memory_fix.py
```

这个脚本会测试：
- 环境变量设置
- 多进程配置
- CUDA内存可用性
- DataLoader创建

## 性能调优建议

### 1. Batch Size调整
- Windows: 建议使用16-32
- Linux: 可以使用64-128
- 根据GPU内存调整

### 2. 模型参数调整
如果仍然遇到内存问题，可以尝试：
- 减小`decoder_dim`（默认128）
- 减小`pf_dim`（默认1024）
- 减小`linear_heads`（默认10）

### 3. 数据预处理优化
- 确保蛋白质embedding文件不会太大
- 考虑使用数据压缩

## 故障排除

### 如果仍然遇到内存问题：

1. **进一步减小batch size**：
   ```bash
   python run_with_memory_fix.py --batch_size 16
   ```

2. **使用单GPU**：
   ```bash
   set CUDA_VISIBLE_DEVICES=0
   python run_with_memory_fix.py
   ```

3. **监控内存使用**：
   ```python
   import torch
   print(f"GPU内存使用: {torch.cuda.memory_allocated()/1024**3:.2f} GB")
   ```

### 如果仍然遇到多进程问题：

1. **确认num_workers=0**：
   检查DataLoader的num_workers参数

2. **检查环境变量**：
   ```python
   import os
   print(os.environ.get('PYTORCH_CUDA_ALLOC_CONF'))
   ```

3. **重启Python进程**：
   有时需要重启Python来应用环境变量

## 注意事项

1. **性能影响**：禁用多进程可能会降低数据加载速度，但可以避免崩溃
2. **内存监控**：建议在训练过程中监控GPU内存使用情况
3. **批次大小**：较小的批次大小可能影响模型收敛，可能需要调整学习率

## 联系支持

如果问题仍然存在，请提供：
- 完整的错误信息
- 系统配置（OS, GPU, PyTorch版本）
- 使用的参数设置
- `test_memory_fix.py`的输出结果
